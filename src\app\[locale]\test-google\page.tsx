"use client";

import { useState } from 'react';
import { openGoogleAuthPopup } from '../../lib/auth/google-simple';

export default function TestGooglePage() {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testGoogleAuth = async () => {
    setLoading(true);
    setResult('');
    
    try {
      console.log('Starting Google Auth test...');
      const code = await openGoogleAuthPopup();
      setResult(`成功获取授权码: ${code.substring(0, 20)}...`);
    } catch (error) {
      console.error('Google Auth test failed:', error);
      setResult(`错误: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setLoading(false);
    }
  };

  const checkConfig = () => {
    const clientId = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID;
    const redirectUri = process.env.NEXT_PUBLIC_GOOGLE_REDIRECT_URI;
    
    setResult(`
配置检查:
- Client ID: ${clientId ? '已设置' : '未设置'}
- Redirect URI: ${redirectUri || '未设置'}
- 当前域名: ${window.location.origin}
    `);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-zinc-900 to-zinc-950 p-8">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold text-white mb-8">Google OAuth 测试</h1>
        
        <div className="space-y-4">
          <button
            onClick={checkConfig}
            className="w-full py-3 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
          >
            检查配置
          </button>
          
          <button
            onClick={testGoogleAuth}
            disabled={loading}
            className={`w-full py-3 px-4 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors ${
              loading ? 'opacity-70 cursor-not-allowed' : ''
            }`}
          >
            {loading ? '测试中...' : '测试 Google 登录'}
          </button>
        </div>
        
        {result && (
          <div className="mt-6 p-4 bg-zinc-800 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-2">结果:</h3>
            <pre className="text-sm text-zinc-300 whitespace-pre-wrap">{result}</pre>
          </div>
        )}
        
        <div className="mt-8 p-4 bg-zinc-800 rounded-lg">
          <h3 className="text-lg font-semibold text-white mb-2">说明:</h3>
          <ul className="text-sm text-zinc-300 space-y-1">
            <li>1. 首先点击"检查配置"确保环境变量正确设置</li>
            <li>2. 然后点击"测试 Google 登录"测试OAuth流程</li>
            <li>3. 如果弹窗被阻止，请允许弹窗后重试</li>
            <li>4. 检查浏览器控制台的详细日志</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
