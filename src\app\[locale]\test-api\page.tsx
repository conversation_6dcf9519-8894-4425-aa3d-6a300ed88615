"use client";

import { useState } from 'react';

export default function TestApiPage() {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testExchangeApi = async () => {
    setLoading(true);
    setResult('');
    
    try {
      // 使用一个测试授权码
      const testCode = '4/0AVMBsJjcF6fzkTBX6KpUGobAVI4rS8Fb9xkpwAsywNiyK_HK4HNVmP5nZ8UDU_UA-5i55w';
      
      const response = await fetch('/api/auth/google/exchange', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          code: testCode,
          redirect_uri: 'http://localhost:3001/auth/google/callback',
        }),
      });

      console.log('API response status:', response.status);
      const responseText = await response.text();
      console.log('API response text:', responseText);

      if (response.ok) {
        setResult(`成功: ${responseText}`);
      } else {
        setResult(`错误 (${response.status}): ${responseText}`);
      }
    } catch (error) {
      console.error('API test failed:', error);
      setResult(`网络错误: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setLoading(false);
    }
  };

  const testEnvApi = async () => {
    setLoading(true);
    setResult('');
    
    try {
      const response = await fetch('/api/test-env');
      const data = await response.json();
      setResult(`环境变量检查: ${JSON.stringify(data, null, 2)}`);
    } catch (error) {
      console.error('Env test failed:', error);
      setResult(`环境变量检查失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-zinc-900 to-zinc-950 p-8">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold text-white mb-8">API 测试</h1>
        
        <div className="space-y-4">
          <button
            onClick={testEnvApi}
            disabled={loading}
            className={`w-full py-3 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors ${
              loading ? 'opacity-70 cursor-not-allowed' : ''
            }`}
          >
            {loading ? '测试中...' : '测试环境变量API'}
          </button>
          
          <button
            onClick={testExchangeApi}
            disabled={loading}
            className={`w-full py-3 px-4 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors ${
              loading ? 'opacity-70 cursor-not-allowed' : ''
            }`}
          >
            {loading ? '测试中...' : '测试令牌交换API'}
          </button>
        </div>
        
        {result && (
          <div className="mt-6 p-4 bg-zinc-800 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-2">结果:</h3>
            <pre className="text-sm text-zinc-300 whitespace-pre-wrap">{result}</pre>
          </div>
        )}
        
        <div className="mt-8 p-4 bg-zinc-800 rounded-lg">
          <h3 className="text-lg font-semibold text-white mb-2">说明:</h3>
          <ul className="text-sm text-zinc-300 space-y-1">
            <li>1. 先测试环境变量API确保配置正确</li>
            <li>2. 然后测试令牌交换API</li>
            <li>3. 查看浏览器控制台和服务器控制台的详细日志</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
