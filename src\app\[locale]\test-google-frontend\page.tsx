"use client";

import { useState } from 'react';
import { openGoogleAuthPopup, exchangeCodeForTokensFrontend, getUserInfo } from '../../lib/auth/google-frontend';

export default function TestGoogleFrontendPage() {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testFullGoogleFlow = async () => {
    setLoading(true);
    setResult('');
    
    try {
      console.log('开始完整的Google登录流程...');
      setResult('步骤1: 打开Google OAuth弹窗...');
      
      // 步骤1: 获取授权码
      const code = await openGoogleAuthPopup();
      setResult(`步骤1完成: 获取授权码 ${code.substring(0, 20)}...\n步骤2: 交换访问令牌...`);
      
      // 步骤2: 交换访问令牌
      const tokens = await exchangeCodeForTokensFrontend(code);
      setResult(prev => `${prev}\n步骤2完成: 获取访问令牌\n步骤3: 获取用户信息...`);
      
      // 步骤3: 获取用户信息
      const userInfo = await getUserInfo(tokens.access_token);
      setResult(prev => `${prev}\n步骤3完成: 获取用户信息\n\n完整结果:\n${JSON.stringify({
        user: {
          id: userInfo.id,
          name: userInfo.name,
          email: userInfo.email,
          picture: userInfo.picture
        },
        tokens: {
          access_token: tokens.access_token.substring(0, 20) + '...',
          token_type: tokens.token_type,
          expires_in: tokens.expires_in
        }
      }, null, 2)}`);
      
    } catch (error) {
      console.error('Google登录流程失败:', error);
      setResult(`错误: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setLoading(false);
    }
  };

  const checkConfig = () => {
    const clientId = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID;
    const clientSecret = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_SECRET;
    const redirectUri = process.env.NEXT_PUBLIC_GOOGLE_REDIRECT_URI;
    
    setResult(`
前端配置检查:
- Client ID: ${clientId ? '已设置' : '未设置'}
- Client Secret (临时): ${clientSecret ? '已设置' : '未设置'}
- Redirect URI: ${redirectUri || '未设置'}
- 当前域名: ${window.location.origin}

⚠️ 注意: Client Secret在前端是临时解决方案，生产环境必须移除！
    `);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-zinc-900 to-zinc-950 p-8">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold text-white mb-8">Google OAuth 前端测试</h1>
        
        <div className="space-y-4">
          <button
            onClick={checkConfig}
            className="w-full py-3 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
          >
            检查前端配置
          </button>
          
          <button
            onClick={testFullGoogleFlow}
            disabled={loading}
            className={`w-full py-3 px-4 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors ${
              loading ? 'opacity-70 cursor-not-allowed' : ''
            }`}
          >
            {loading ? '测试中...' : '测试完整Google登录流程'}
          </button>
        </div>
        
        {result && (
          <div className="mt-6 p-4 bg-zinc-800 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-2">结果:</h3>
            <pre className="text-sm text-zinc-300 whitespace-pre-wrap">{result}</pre>
          </div>
        )}
        
        <div className="mt-8 p-4 bg-zinc-800 rounded-lg">
          <h3 className="text-lg font-semibold text-white mb-2">说明:</h3>
          <ul className="text-sm text-zinc-300 space-y-1">
            <li>1. 这是前端版本的Google OAuth测试</li>
            <li>2. 绕过了服务器端的网络连接问题</li>
            <li>3. ⚠️ 客户端密钥暴露在前端，仅用于开发测试</li>
            <li>4. 生产环境必须使用服务器端版本</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
