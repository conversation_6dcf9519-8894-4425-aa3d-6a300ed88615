"use client";

import { useState } from 'react';
import { signInWithGoogleButton, parseJWTCredential } from '../../lib/auth/google-gsi';

export default function TestIntegrationPage() {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testIntegratedGoogleLogin = async () => {
    setLoading(true);
    setResult('');

    try {
      console.log('开始集成的Google登录测试...');
      setResult('步骤1: 显示Google登录按钮...');

      // 使用集成的按钮式登录
      const credential = await signInWithGoogleButton();
      setResult(prev => `${prev}\n步骤2: 获取JWT credential成功\n步骤3: 解析用户信息...`);

      // 解析JWT获取用户信息
      const userInfo = parseJWTCredential(credential);

      if (!userInfo) {
        setResult(prev => `${prev}\n步骤3失败: 无法解析JWT credential`);
        return;
      }

      setResult(prev => `${prev}\n步骤3完成: 解析用户信息成功\n\n✅ 集成测试成功！\n\n用户信息:\n${JSON.stringify({
        googleId: userInfo.sub,
        email: userInfo.email,
        name: userInfo.name,
        picture: userInfo.picture,
        email_verified: userInfo.email_verified
      }, null, 2)}\n\ncredential (前50字符): ${credential.substring(0, 50)}...`);

    } catch (error) {
      console.error('集成Google登录失败:', error);
      setResult(`❌ 错误: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-zinc-900 to-zinc-950 p-8">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold text-white mb-8">Google登录集成测试</h1>
        
        <div className="space-y-4">
          <button
            onClick={testIntegratedGoogleLogin}
            disabled={loading}
            className={`w-full py-3 px-4 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors ${
              loading ? 'opacity-70 cursor-not-allowed' : ''
            }`}
          >
            {loading ? '测试中...' : '🎯 测试集成的Google登录'}
          </button>
        </div>
        
        {result && (
          <div className="mt-6 p-4 bg-zinc-800 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-2">测试结果:</h3>
            <pre className="text-sm text-zinc-300 whitespace-pre-wrap">{result}</pre>
          </div>
        )}
        
        <div className="mt-8 p-4 bg-zinc-800 rounded-lg">
          <h3 className="text-lg font-semibold text-white mb-2">说明:</h3>
          <ul className="text-sm text-zinc-300 space-y-1">
            <li>• 这个测试页面使用与主登录系统相同的Google登录方法</li>
            <li>• 测试成功后，可以确认主登录系统中的Google登录也能正常工作</li>
            <li>• 使用按钮式登录，更稳定可靠</li>
            <li>• 弹窗样式已优化，符合网站设计风格</li>
          </ul>
        </div>
        
        <div className="mt-6 p-4 bg-blue-900/20 border border-blue-500/30 rounded-lg">
          <h3 className="text-lg font-semibold text-blue-300 mb-2">下一步:</h3>
          <ul className="text-sm text-blue-200 space-y-1">
            <li>1. 测试成功后，访问游戏页面测试实际登录流程</li>
            <li>2. 确认Google登录能正确调用后端API</li>
            <li>3. 验证登录后的区服角色选择功能</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
