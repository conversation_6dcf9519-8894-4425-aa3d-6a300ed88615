"use client";

import { useState } from 'react';
import { signInWithGoogleButton, parseJWTCredential } from '../../lib/auth/google-gsi';

export default function TestIntegrationPage() {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testIntegratedGoogleLogin = async () => {
    setLoading(true);
    setResult('');

    try {
      console.log('开始集成的Google登录测试...');
      setResult('步骤1: 尝试一键Google登录...');

      // 使用智能Google登录（一键登录 + 按钮式回退）
      let credential: string;

      try {
        credential = await signInWithGoogleButton();
        setResult(prev => `${prev}\n✅ 一键登录成功！\n步骤2: 获取JWT credential成功\n步骤3: 解析用户信息...`);
      } catch (error) {
        setResult(prev => `${prev}\n⚠️ 一键登录失败，显示登录按钮...\n步骤2: 等待用户点击Google按钮...`);

        // 动态导入备用方法
        const { signInWithGoogleButtonFallback } = await import('../../lib/auth/google-gsi');
        credential = await signInWithGoogleButtonFallback();
        setResult(prev => `${prev}\n✅ 按钮登录成功！\n步骤3: 获取JWT credential成功\n步骤4: 解析用户信息...`);
      }

      // 解析JWT获取用户信息
      const userInfo = parseJWTCredential(credential);

      if (!userInfo) {
        setResult(prev => `${prev}\n解析失败: 无法解析JWT credential`);
        return;
      }

      setResult(prev => `${prev}\n解析完成: 用户信息解析成功\n\n✅ 智能Google登录测试成功！\n\n用户信息:\n${JSON.stringify({
        googleId: userInfo.sub,
        email: userInfo.email,
        name: userInfo.name,
        picture: userInfo.picture,
        email_verified: userInfo.email_verified
      }, null, 2)}\n\ncredential (前50字符): ${credential.substring(0, 50)}...`);

    } catch (error) {
      console.error('集成Google登录失败:', error);
      setResult(`❌ 错误: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-zinc-900 to-zinc-950 p-8">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold text-white mb-8">Google登录集成测试</h1>
        
        <div className="space-y-4">
          <button
            onClick={testIntegratedGoogleLogin}
            disabled={loading}
            className={`w-full py-3 px-4 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors ${
              loading ? 'opacity-70 cursor-not-allowed' : ''
            }`}
          >
            {loading ? '测试中...' : '🚀 测试智能Google登录'}
          </button>
        </div>
        
        {result && (
          <div className="mt-6 p-4 bg-zinc-800 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-2">测试结果:</h3>
            <pre className="text-sm text-zinc-300 whitespace-pre-wrap">{result}</pre>
          </div>
        )}
        
        <div className="mt-8 p-4 bg-zinc-800 rounded-lg">
          <h3 className="text-lg font-semibold text-white mb-2">说明:</h3>
          <ul className="text-sm text-zinc-300 space-y-1">
            <li>• 智能Google登录：优先尝试一键直接登录</li>
            <li>• 如果一键登录失败，自动回退到按钮式登录</li>
            <li>• 减少用户操作步骤，提升登录体验</li>
            <li>• 兼容性更好，适应不同浏览器环境</li>
            <li>• 弹窗样式已优化，符合网站设计风格</li>
          </ul>
        </div>
        
        <div className="mt-6 p-4 bg-blue-900/20 border border-blue-500/30 rounded-lg">
          <h3 className="text-lg font-semibold text-blue-300 mb-2">智能登录流程:</h3>
          <ul className="text-sm text-blue-200 space-y-1">
            <li>1. 🚀 优先尝试一键直接跳转到Google登录</li>
            <li>2. 🎯 如果失败，自动显示Google登录按钮</li>
            <li>3. ✅ 用户体验最佳，操作步骤最少</li>
            <li>4. 🔧 兼容各种浏览器和网络环境</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
