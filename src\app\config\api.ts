/**
 * API配置文件
 */

// 判断当前环境
const isDevelopment = process.env.NODE_ENV === 'development';

// API基础URL
export const API_BASE_URL = isDevelopment
  ? 'http://localhost:48080/admin-api'
  : 'https://admin.manmanyouhudong.com/admin-api';


  export const APP_API_BASE_URL = isDevelopment
  ? 'http://localhost:48080/app-api'
  : 'https://admin.manmanyouhudong.com/app-api';
 
// 站点ID
export const SITE_ID = 1;

// Google OAuth 配置
export const GOOGLE_OAUTH_CONFIG = {
  CLIENT_ID: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID || '',
  REDIRECT_URI: process.env.NEXT_PUBLIC_GOOGLE_REDIRECT_URI || `${typeof window !== 'undefined' ? window.location.origin : ''}/auth/google/callback`,
  SCOPE: 'openid email profile'
};

// API端点
export const API_ENDPOINTS = {
  // 获取站点信息和游戏详情（包括商品）
  GET_SITE_INFO: `${API_BASE_URL}/site/paySite/getSiteInfo`,

  // 获取首页游戏列表
  GET_GAMES_LIST: `${API_BASE_URL}/site/paySite/getGamePayList`,

  // 获取支付配置列表
  GET_PAYMENT_CONFIGS: `${API_BASE_URL}/site/pay-config/list-for-payment`,

  // 确认支付
  POST_CONFIRM_PAYMENT:`${APP_API_BASE_URL}/pay/confirm-payment`,

  // Google登录
  GOOGLE_LOGIN: `${API_BASE_URL}/erp/game/google-login`
};