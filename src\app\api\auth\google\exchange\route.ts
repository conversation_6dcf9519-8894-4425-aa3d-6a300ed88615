import { NextRequest, NextResponse } from 'next/server';

/**
 * 安全的Google OAuth令牌交换API
 * 客户端密钥只在服务器端使用，不会暴露给前端
 */
export async function POST(request: NextRequest) {
  try {
    console.log('Google OAuth exchange API called');
    const { code, redirect_uri } = await request.json();

    console.log('Received parameters:', {
      code: code ? 'present' : 'missing',
      redirect_uri
    });

    if (!code || !redirect_uri) {
      console.error('Missing required parameters:', { code: !!code, redirect_uri: !!redirect_uri });
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // 验证环境变量
    const clientId = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID;
    const clientSecret = process.env.GOOGLE_CLIENT_SECRET;

    console.log('Environment variables check:', {
      clientId: clientId ? 'present' : 'missing',
      clientSecret: clientSecret ? 'present' : 'missing'
    });

    if (!clientId || !clientSecret) {
      console.error('Missing Google OAuth configuration');
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    // 向Google交换授权码获取访问令牌
    const tokenResponse = await fetch('https://oauth2.googleapis.com/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        client_id: clientId,
        client_secret: clientSecret, // 客户端密钥只在服务器端使用
        code,
        grant_type: 'authorization_code',
        redirect_uri,
      }),
    });

    if (!tokenResponse.ok) {
      const errorData = await tokenResponse.text();
      console.error('Google token exchange failed:', errorData);
      return NextResponse.json(
        { error: 'Failed to exchange code for tokens' },
        { status: 400 }
      );
    }

    const tokens = await tokenResponse.json();

    // 返回令牌给前端（不包含refresh_token以增加安全性）
    return NextResponse.json({
      access_token: tokens.access_token,
      id_token: tokens.id_token,
      scope: tokens.scope,
      token_type: tokens.token_type,
      expires_in: tokens.expires_in,
    });

  } catch (error) {
    console.error('Google OAuth exchange error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
