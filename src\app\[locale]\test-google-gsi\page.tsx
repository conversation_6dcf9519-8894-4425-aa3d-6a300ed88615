"use client";

import { useState } from 'react';
import { openGoogleSignInPopup, parseJWTCredential } from '../../lib/auth/google-gsi';

export default function TestGoogleGSIPage() {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testGoogleGSI = async () => {
    setLoading(true);
    setResult('');
    
    try {
      console.log('开始Google Identity Services登录...');
      setResult('步骤1: 打开Google登录弹窗...');
      
      // 获取JWT credential
      const credential = await openGoogleSignInPopup();
      setResult(prev => `${prev}\n步骤1完成: 获取JWT credential\n步骤2: 解析用户信息...`);
      
      // 解析JWT获取用户信息
      const userInfo = parseJWTCredential(credential);
      
      if (!userInfo) {
        setResult(prev => `${prev}\n步骤2失败: 无法解析JWT credential`);
        return;
      }
      
      setResult(prev => `${prev}\n步骤2完成: 解析用户信息成功\n\n完整结果:\n${JSON.stringify({
        credential: credential.substring(0, 50) + '...',
        userInfo: {
          sub: userInfo.sub, // Google用户ID
          email: userInfo.email,
          name: userInfo.name,
          picture: userInfo.picture,
          email_verified: userInfo.email_verified,
          iss: userInfo.iss, // 发行者
          aud: userInfo.aud, // 受众
          exp: userInfo.exp, // 过期时间
          iat: userInfo.iat  // 签发时间
        }
      }, null, 2)}`);
      
    } catch (error) {
      console.error('Google Identity Services登录失败:', error);
      setResult(`错误: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setLoading(false);
    }
  };

  const checkConfig = () => {
    const clientId = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID;
    
    setResult(`
Google Identity Services配置检查:
- Client ID: ${clientId ? '已设置' : '未设置'}
- Client ID值: ${clientId || '未设置'}
- 当前域名: ${window.location.origin}

✅ 优势: 
- 不需要客户端密钥
- 直接获取JWT credential
- 更安全的实现方式
- Google推荐的新方法
    `);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-zinc-900 to-zinc-950 p-8">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold text-white mb-8">Google Identity Services 测试</h1>
        
        <div className="space-y-4">
          <button
            onClick={checkConfig}
            className="w-full py-3 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
          >
            检查GSI配置
          </button>
          
          <button
            onClick={testGoogleGSI}
            disabled={loading}
            className={`w-full py-3 px-4 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg transition-colors ${
              loading ? 'opacity-70 cursor-not-allowed' : ''
            }`}
          >
            {loading ? '测试中...' : '测试Google Identity Services登录'}
          </button>
        </div>
        
        {result && (
          <div className="mt-6 p-4 bg-zinc-800 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-2">结果:</h3>
            <pre className="text-sm text-zinc-300 whitespace-pre-wrap">{result}</pre>
          </div>
        )}
        
        <div className="mt-8 p-4 bg-zinc-800 rounded-lg">
          <h3 className="text-lg font-semibold text-white mb-2">说明:</h3>
          <ul className="text-sm text-zinc-300 space-y-1">
            <li>1. 这是Google Identity Services的测试</li>
            <li>2. 直接获取JWT格式的credential</li>
            <li>3. ✅ 不需要客户端密钥，更安全</li>
            <li>4. ✅ Google推荐的新登录方式</li>
            <li>5. credential就是后端需要的access_token</li>
          </ul>
        </div>
        
        <div className="mt-6 p-4 bg-blue-900/20 border border-blue-500/30 rounded-lg">
          <h3 className="text-lg font-semibold text-blue-300 mb-2">JWT Credential说明:</h3>
          <ul className="text-sm text-blue-200 space-y-1">
            <li>• <strong>sub</strong>: Google用户ID (相当于googleId)</li>
            <li>• <strong>email</strong>: 用户邮箱</li>
            <li>• <strong>name</strong>: 用户姓名</li>
            <li>• <strong>picture</strong>: 用户头像URL</li>
            <li>• <strong>email_verified</strong>: 邮箱是否已验证</li>
            <li>• <strong>iss</strong>: 发行者 (accounts.google.com)</li>
            <li>• <strong>aud</strong>: 受众 (您的客户端ID)</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
